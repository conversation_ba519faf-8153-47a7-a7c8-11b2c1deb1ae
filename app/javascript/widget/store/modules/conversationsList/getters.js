export const getters = {
  getConversations: state => state.conversations,
  getConversationsSortedByAssigneeLastSeen: state => {
    return [...state.conversations].sort((a, b) => {
      // Sort by assignee_last_seen_at in descending order (most recent first)
      // Handle null values by putting them at the end
      if (!a.assignee_last_seen_at && !b.assignee_last_seen_at) return 0;
      if (!a.assignee_last_seen_at) return 1;
      if (!b.assignee_last_seen_at) return -1;
      return b.assignee_last_seen_at - a.assignee_last_seen_at;
    });
  },
  getUIFlags: state => state.uiFlags,
  isFetching: state => state.uiFlags.isFetching,
  getMeta: state => state.meta,
  hasMorePages: state => state.meta.hasMorePages,
};
