export const mutations = {
  setConversations(state, conversations) {
    state.conversations = conversations;
  },

  appendConversations(state, conversations) {
    state.conversations.push(...conversations);
  },

  setFetching(state, isFetching) {
    state.uiFlags.isFetching = isFetching;
  },

  setMeta(state, meta) {
    state.meta = { ...state.meta, ...meta };
  },

  addConversation(state, conversation) {
    state.conversations.push(conversation);
  },

  updateConversation(state, updatedConversation) {
    const index = state.conversations.findIndex(
      conv => conv.id === updatedConversation.id
    );
    if (index !== -1) {
      state.conversations.splice(index, 1, updatedConversation);
    }
  },

  removeConversation(state, conversationId) {
    state.conversations = state.conversations.filter(
      conv => conv.id !== conversationId
    );
  },
};
