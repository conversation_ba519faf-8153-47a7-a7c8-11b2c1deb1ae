<script>
import { mapGetters, mapActions } from 'vuex';
import Spinner from 'shared/components/Spinner.vue';
import { formatUnixDate } from 'shared/helpers/DateHelper';

export default {
  name: 'ConversationsList',
  components: {
    Spinner,
  },
  computed: {
    ...mapGetters({
      conversations: 'conversationsList/getConversationsSortedByAssigneeLastSeen',
      isFetching: 'conversationsList/isFetching',
    }),
  },
  mounted() {
    this.fetchConversations();
  },
  methods: {
    ...mapActions('conversationsList', ['fetchConversations']),

    formatDate(timestamp) {
      return formatUnixDate(timestamp);

    },
    // Used for status
    // getStatusColor(status) {
    //   const colors = {
    //     open: 'bg-green-500',
    //     resolved: 'bg-gray-500',
    //     pending: 'bg-yellow-500',
    //     snoozed: 'bg-blue-500',
    //   };
    //   return colors[status] || 'bg-gray-500';
    // },

    selectConversation(conversation) {
      // Emit event to parent or handle conversation selection
      this.$emit('select-conversation', conversation);
    },

    getLastMessagePreview(conversation) {
      if (!conversation.last_message) return 'No messages yet';

      const content = conversation.last_message.content;
      if (content.length > 50) {
        return content.substring(0, 50) + '...';
      }
      return content;
    },

    getConversationDisplayedId(conversation) {
      let displayedId = conversation.id;
      if(conversation.contact_inbox.source_id) {
        displayedId += '-'+conversation.contact_inbox.source_id;
      }
      return displayedId;
    },
  },
};
</script>

<template>
  <div class="conversations-list">
    <div v-if="isFetching" class="flex justify-center py-4">
      <Spinner />
    </div>

    <div v-else-if="conversations.length === 0" class="text-center py-4 text-gray-500">

    </div>

    <div v-else class="space-y-2 max-h-full overflow-y-auto">
      <div
        v-for="conversation in conversations"
        :key="conversation.id"
        class="conversation-item p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors"
        @click="selectConversation(conversation)"
      >
        <div class="flex items-start justify-between">
          <div class="flex-1 min-w-0">
            <div class="flex items-center gap-2 mb-1">
              <!-- <span
                class="w-2 h-2 rounded-full"
                :class="getStatusColor(conversation.status)"
              ></span> -->
              <span class="text-sm font-medium text-n-brand">
                #{{ getConversationDisplayedId(conversation) }}
              </span>
              <!-- <span class="text-xs text-gray-500 capitalize">
                {{ conversation.status }}
              </span> -->
            </div>

            <div class="text-sm text-gray-600 mb-1">
              {{ getLastMessagePreview(conversation) }}
            </div>

            <div class="flex items-center text-xs text-gray-500 italic">


              <div v-if="conversation.contact_last_seen_at">
                {{ $t('LAST_SEEN') }}: {{ formatDate(conversation.contact_last_seen_at) }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.conversations-list {
  max-height: 400px;
  overflow-y: auto;
}

.conversation-item:hover {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
</style>
